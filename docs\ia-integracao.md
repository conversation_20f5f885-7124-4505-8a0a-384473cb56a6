# Documentação: Integração de IA no Gestor Brechó

## Visão Geral

Este documento descreve a integração de funcionalidades de Inteligência Artificial no sistema Gestor Brechó, com foco inicial na funcionalidade de consulta SQL em linguagem natural através de um chatbot.

## Arquitetura

A implementação segue uma arquitetura cliente-servidor:

### Frontend (React)
- **Componentes de UI**: `ChatWidget`, `ChatMessage`, `ChatInput`, `FloatingChatIcon`
- **Gerenciamento de Estado**: `IAContext`, `useIAChat`, `useAuth`
- **Serviços**: `iaService.ts` para comunicação com o backend

### Backend (Supabase Edge Functions)
- **Edge Function `chat-assistant`**: Processamento de mensagens usando DeepSeek API
- **Supabase**: 
  - Tabela `ia_chat_historico` para armazenamento de conversas
  - Função `execute_safe_query` para execução segura de consultas SQL
  - Políticas RLS para garantir segurança dos dados
  - Secrets para armazenamento seguro de API keys

## Fluxo de Funcionamento

1. O usuário interage com o chatbot através da interface do aplicativo
2. A mensagem é enviada para a Edge Function `chat-assistant` via `iaService.ts`
3. A Edge Function processa a mensagem usando DeepSeek API para:
   - Interpretar a intenção do usuário
   - Gerar resposta contextualizada sobre o sistema
4. A resposta é retornada ao usuário
5. Todo o histórico é armazenado na tabela `ia_chat_historico`

## Segurança

A implementação prioriza a segurança através de:

- **Autenticação**: Uso do JWT existente em todas as comunicações
- **Autorização**: Políticas RLS no Supabase garantem que usuários acessem apenas seus próprios dados
- **Execução Segura**: Queries SQL são executadas através de uma função SECURITY DEFINER controlada
- **Validação**: Sanitização e validação de inputs do usuário

## Estrutura de Arquivos

```
src/
├── components/
│   └── ia/
│       ├── ChatWidget.tsx       # Componente principal do chat
│       ├── ChatMessage.tsx      # Componente para mensagens individuais
│       ├── ChatInput.tsx        # Input para perguntas do usuário
│       └── FloatingChatIcon.tsx # Ícone flutuante de acesso rápido
├── contexts/
│   └── IAContext.tsx            # Contexto para gerenciar estado do chat
├── hooks/
│   └── useIAChat.ts             # Hook para lógica de chat
└── services/
    └── iaService.ts             # Serviço para comunicação com n8n
```

## Database Schema

### Tabela: ia_chat_historico
```sql
CREATE TABLE ia_chat_historico (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    user_message TEXT NOT NULL,
    assistant_message TEXT NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    metadata JSONB DEFAULT '{}'::jsonb
);
```

## Configuração da Edge Function

### Deploy e Configuração

1. **Configurar Secret da API**:
   ```bash
   supabase secrets set DEEPSEEK_API_KEY=sua_chave_aqui
   ```

2. **Deploy da Função**:
   ```bash
   supabase functions deploy chat-assistant
   ```

3. **Aplicar Migrations** (se necessário):
   ```bash
   supabase db push
   ```

### Estrutura da Edge Function

- **Autenticação**: Validação automática via Supabase Auth
- **Processamento**: Integração direta com DeepSeek API
- **Contexto**: Sistema mantém histórico das últimas 5 mensagens
- **Resposta**: Formato JSON estruturado

## Próximos Passos

### Fases Futuras de Implementação:
1. **Integração com dados do sistema**: Permitir que a IA consulte dados reais do banco
2. **Recomendações baseadas em histórico**: Sugestão de produtos com base no histórico de compras
3. **Alertas preditivos de estoque**: Previsão de quando um produto ficará fora de estoque
4. **Análises avançadas**: Insights automáticos sobre vendas e tendências

### Melhorias Planejadas:
- Cache de consultas frequentes para melhorar performance
- Interface para administradores visualizarem consultas populares
- Expansão do modelo de linguagem para suportar mais tipos de perguntas 