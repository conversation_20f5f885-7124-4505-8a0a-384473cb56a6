import { serve } from "https://deno.land/std@0.177.0/http/server.ts"
import { corsHeaders } from "../_shared/cors.ts"

const DEEPSEEK_API_KEY = Deno.env.get('DEEPSEEK_API_KEY')
const DEEPSEEK_API_URL = 'https://api.deepseek.com/v1/chat/completions'

interface ChatMessage {
  role: 'user' | 'assistant' | 'system'
  content: string
}

interface ChatRequest {
  idCliente: string
  mensagem: string
  historico?: Array<{
    role: string
    content: string
  }>
}

serve(async (req) => {
  // Handle CORS
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Verificar se a API key está configurada
    if (!DEEPSEEK_API_KEY) {
      throw new Error('DEEPSEEK_API_KEY não configurada')
    }

    // Parse request body
    const { idCliente, mensagem, historico = [] } = await req.json() as ChatRequest

    // Validar entrada
    if (!idCliente || !mensagem) {
      throw new Error('idCliente e mensagem são obrigatórios')
    }

    // Construir contexto do sistema
    const systemPrompt = `Você é um assistente inteligente para gestão de brechós. 
Você tem acesso aos dados do sistema e pode ajudar com:
- Análise de vendas e faturamento
- Controle de estoque
- Informações sobre clientes
- Relatórios financeiros
- Gestão de fornecedores e produtos

Responda de forma clara, objetiva e em português brasileiro.
Quando possível, forneça dados específicos e insights úteis.`

    // Preparar mensagens para a API
    const messages: ChatMessage[] = [
      { role: 'system', content: systemPrompt },
      ...historico.slice(-5).map(msg => ({
        role: msg.role as 'user' | 'assistant',
        content: msg.content
      })),
      { role: 'user', content: mensagem }
    ]

    // Chamar API DeepSeek
    const response = await fetch(DEEPSEEK_API_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${DEEPSEEK_API_KEY}`,
      },
      body: JSON.stringify({
        model: 'deepseek-chat',
        messages,
        temperature: 0.7,
        max_tokens: 1000,
      }),
    })

    if (!response.ok) {
      const error = await response.text()
      console.error('Erro na API DeepSeek:', error)
      throw new Error(`Erro na API: ${response.status}`)
    }

    const data = await response.json()
    const assistantMessage = data.choices[0]?.message?.content || 'Desculpe, não consegui processar sua mensagem.'

    // Salvar no histórico (opcional - pode ser feito no frontend)
    // TODO: Implementar salvamento no banco de dados se necessário

    // Retornar resposta
    return new Response(
      JSON.stringify({ 
        resposta: assistantMessage,
        response: assistantMessage // manter compatibilidade
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      }
    )

  } catch (error) {
    console.error('Erro na edge function:', error)
    
    return new Response(
      JSON.stringify({ 
        error: error.message || 'Erro ao processar mensagem',
        resposta: 'Desculpe, ocorreu um erro ao processar sua mensagem. Por favor, tente novamente.'
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500,
      }
    )
  }
})