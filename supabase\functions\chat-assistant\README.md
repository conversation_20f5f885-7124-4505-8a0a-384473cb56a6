# Edge Function: Chat Assistant

Esta edge function implementa o assistente de IA do Gestor Brechó usando a API DeepSeek.

## Configuração

### 1. Configurar Secret no Supabase

Primeiro, configure a chave da API DeepSeek como secret no Supabase:

```bash
# Via Supabase CLI
supabase secrets set DEEPSEEK_API_KEY=***********************************
```

Ou pelo Dashboard do Supabase:
1. Acesse seu projeto no Supabase
2. Vá em Settings > Edge Functions
3. Adicione o secret `DEEPSEEK_API_KEY` com o valor da chave

### 2. Deploy da Edge Function

```bash
# Deploy da função
supabase functions deploy chat-assistant

# Verificar se foi deployada
supabase functions list
```

### 3. Executar Migration (se necessário)

Se a tabela `ia_chat_historico` ainda não existir:

```bash
# Aplicar migration
supabase db push
```

## Testando a Função

### Via cURL

```bash
curl -X POST https://[SEU_PROJETO].supabase.co/functions/v1/chat-assistant \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer [SEU_ANON_KEY]" \
  -d '{
    "idCliente": "user-123",
    "mensagem": "Qual foi o total de vendas do último mês?",
    "historico": []
  }'
```

### Via JavaScript

```javascript
const response = await fetch('https://[SEU_PROJETO].supabase.co/functions/v1/chat-assistant', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${supabaseAnonKey}`,
  },
  body: JSON.stringify({
    idCliente: userId,
    mensagem: 'Sua pergunta aqui',
    historico: []
  })
})

const data = await response.json()
console.log(data.resposta)
```

## Estrutura da Requisição

```typescript
{
  idCliente: string       // ID do usuário
  mensagem: string        // Mensagem do usuário
  historico?: Array<{     // Histórico opcional (últimas 5 mensagens)
    role: string
    content: string
  }>
}
```

## Estrutura da Resposta

```typescript
{
  resposta: string        // Resposta da IA
  response: string        // Duplicado para compatibilidade
}
```

## Monitoramento

Para verificar logs da função:

```bash
supabase functions logs chat-assistant
```

## Troubleshooting

1. **Erro 500**: Verifique se o secret `DEEPSEEK_API_KEY` está configurado
2. **Erro 401**: Verifique o token de autenticação
3. **Timeout**: A API DeepSeek pode demorar alguns segundos para responder

## Notas de Segurança

- A função valida que `idCliente` e `mensagem` são obrigatórios
- O histórico é limitado às últimas 5 mensagens para economizar tokens
- CORS está configurado para aceitar requisições de qualquer origem (ajustar em produção)