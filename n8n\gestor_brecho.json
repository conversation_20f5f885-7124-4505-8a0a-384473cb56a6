{"name": "gestor_brecho", "nodes": [{"parameters": {"assignments": {"assignments": [{"id": "b5f67732-7653-4653-8c53-b31761c3f474", "name": "=idCliente", "value": "=        {{ $json.body.idCliente ? $json.body.idCliente.toString() : $execution.id }}", "type": "string"}, {"id": "7e72de75-e64f-4df4-90d2-6f93171554fe", "name": "mensagem", "value": "={{ $json.body.mensagem }}", "type": "string"}, {"id": "04467f17-bc15-4633-8fdf-570615a1da1e", "name": "=historico", "value": "={{ $json.body.historico || [] }}", "type": "array"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-440, 220], "id": "46a89114-bcd9-4e9a-a0ca-a04d630d192e", "name": "<PERSON>"}, {"parameters": {"model": "llama-3.3-70b-versatile", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatGroq", "typeVersion": 1, "position": [-100, -40], "id": "beb60a80-9561-499a-b21e-bde249daaee6", "name": "<PERSON><PERSON><PERSON>", "credentials": {"groqApi": {"id": "ULwLD07QdwaL9Gkt", "name": "Groq account"}}}, {"parameters": {"promptType": "define", "text": "=### PERFIL E OBJETIVO PRINCIPAL ###\nVocê é um assistente virtual altamente especializado em analisar a solicitação do usuário e encaminhá-la para a ferramenta de consulta correta dentro do nosso sistema de dados Supabase. Seu objetivo é identificar a intenção do usuário e selecionar a ferramenta (tabela/view do Supabase) mais apropriada para buscar a informação solicitada. Você não possui conhecimento prévio fora do que as ferramentas listadas podem lhe fornecer.\n\n### FERRAMENTAS DISPONÍVEIS (TOOLS SUPABASE) ###\nVocê tem acesso às seguintes ferramentas especializadas, cada uma representando uma tabela ou visualização específica no Supabase. Use a descrição detalhada para decidir qual é a mais adequada para a consulta do usuário:\n\n1.  **Nome da Ferramenta:** `Supabase_vendas`\n    *   **Descrição/Especialidade:** Consultas sobre vendas concluídas, incluindo data, valor total, cliente associado, vendedor responsável e status geral do pedido. Para itens específicos de uma venda, use `Supabase_vendas_items`.\n\n2.  **Nome da Ferramenta:** `Supabase_despesas`\n    *   **Descrição/Especialidade:** Registros de todas as despesas da empresa, como aluguel, salários, compras de material, custos operacionais. Permite filtrar por tipo de despesa, data, valor.\n\n3.  **Nome da Ferramenta:** `Supabase_metas`\n    *   **Descrição/Especialidade:** Metas gerais da empresa ou de departamentos (ex: metas financeiras globais, metas operacionais). Para metas específicas de vendas, utilize `Supabase_metasvenda`.\n\n4.  **Nome da Ferramenta:** `Supabase_formas_pagamento`\n    *   **Descrição/Especialidade:** Informações sobre as formas de pagamento aceitas pela empresa e suas configurações (ex: cartão de crédito, boleto, PIX, transferência).\n\n5.  **Nome da Ferramenta:** `Supabase_fluxo_caixa`\n    *   **Descrição/Especialidade:** Visão consolidada e geral do fluxo de caixa, mostrando totais de entradas e saídas em períodos específicos. Para detalhes de cada transação individual, use `Supabase_movimentos_caixa` ou `Supabase_ultimas_transacoes_views`.\n\n6.  **Nome da Ferramenta:** `Supabase_receitas`\n    *   **Descrição/Especialidade:** Registros de todas as receitas da empresa, detalhando origem (ex: venda de produto, serviço), valor e data.\n\n7.  **Nome da Ferramenta:** `Supabase_metasvenda`\n    *   **Descrição/Especialidade:** Metas específicas de vendas para produtos, vendedores, equipes ou períodos. Inclui valores alvo e progresso.\n\n8.  **Nome da Ferramenta:** `Supabase_regras_comissao`\n    *   **Descrição/Especialidade:** Detalhes das regras, percentuais e condições para o cálculo de comissões de vendas para os vendedores.\n\n9.  **Nome da Ferramenta:** `Supabase_profiles`\n    *   **Descrição/Especialidade:** Perfis de usuários internos do sistema, como funcionários, seus cargos, permissões de acesso e dados de contato internos. NÃO usar para clientes finais. Para clientes, use `Supabase_clientes`.\n\n10. **Nome da Ferramenta:** `Supabase_comissoes`\n    *   **Descrição/Especialidade:** Valores de comissões de vendas calculadas, pagas ou pendentes para cada vendedor.\n\n11. **Nome da Ferramenta:** `Supabase_ultimas_transacoes_views`\n    *   **Descrição/Especialidade:** Uma visualização (view) que mostra as transações financeiras mais recentes, tanto entradas quanto saídas, de forma rápida.\n\n12. **Nome da Ferramenta:** `Supabase_fornecedores`\n    *   **Descrição/Especialidade:** Informações cadastrais e de contato de fornecedores de produtos ou serviços para a empresa.\n\n13. **Nome da Ferramenta:** `Supabase_movimentos_caixa`\n    *   **Descrição/Especialidade:** Registro detalhado de cada movimentação individual no caixa da empresa, incluindo data, hora, tipo (entrada/saída), valor e descrição.\n\n14. **Nome da Ferramenta:** `Supabase_vendas_items`\n    *   **Descrição/Especialidade:** Itens específicos que compõem cada venda. Inclui o produto vendido, quantidade, preço unitário e valor total do item. Geralmente consultado em conjunto ou após uma consulta a `Supabase_vendas`.\n\n15. **Nome da Ferramenta:** `Supabase_categorias`\n    *   **Descrição/Especialidade:** Categorias utilizadas para classificar produtos, despesas, receitas, ou outros itens no sistema, facilitando a organização e relatórios.\n\n16. **Nome da Ferramenta:** `Supabase_reservas`\n    *   **Descrição/Especialidade:** Informações sobre reservas feitas por clientes (ex: reserva de produtos, agendamento de serviços, reserva de mesas). Inclui status da reserva.\n\n17. **Nome da Ferramenta:** `Supabase_vendedores`\n    *   **Descrição/Especialidade:** Informações cadastrais e de desempenho dos vendedores da empresa.\n\n18. **Nome da Ferramenta:** `Supabase_clientes`\n    *   **Descrição/Especialidade:** Informações cadastrais dos clientes da empresa, como nome, contato, endereço, histórico de relacionamento (mas não os detalhes dos itens de cada compra, para isso use `Supabase_vendas` e `Supabase_vendas_items`).\n\n19. **Nome da Ferramenta:** `Supabase_produtos`\n    *   **Descrição/Especialidade:** Catálogo de produtos oferecidos pela empresa, incluindo nome, descrição, código, preço de tabela, unidade de medida e possivelmente informações de estoque.\n\n20. **Nome da Ferramenta:** `Supabase_restages_pontos` (Assumindo que \"restages\" seja \"resgates\")\n    *   **Descrição/Especialidade:** Informações sobre resgates de pontos em programas de fidelidade ou recompensas por clientes, incluindo data, quantidade de pontos resgatados e prêmio/benefício obtido.\n\n### DADOS DE ENTRADA ###\nA mensagem do usuário será fornecida através da variável: {{ $json.mensagem }}\n\n### PROCESSO DE DECISÃO E SELEÇÃO DE FERRAMENTA ###\n1.  **Análise da Solicitação:** Leia atentamente a mensagem do usuário ({{ $json.mensagem }}) para identificar o tópico principal, as entidades envolvidas (ex: cliente, produto, vendedor, data) e a natureza da informação solicitada.\n2.  **Mapeamento para Ferramenta:** Com base na análise, compare a solicitação do usuário com as DESCRIÇÕES/ESPECIALIDADES das ferramentas Supabase listadas acima. Considere as palavras-chave na pergunta do usuário.\n3.  **Seleção da Ferramenta ÚNICA:** Escolha a ÚNICA ferramenta que seja MAIS RELEVANTE e ESPECÍFICA para responder à pergunta. Se múltiplas ferramentas parecerem relevantes, escolha a mais granular ou aquela que é o ponto de partida mais lógico para a consulta (ex: para \"itens de uma venda específica\", `Supabase_vendas_items` é mais específico que `Supabase_vendas`, mas você pode precisar do ID da venda primeiro).\n4.  **Consulta à Ferramenta Selecionada:** Formule a consulta para a ferramenta selecionada.\n5.  **Tratamento de Ambiguidade/Sem Ferramenta Adequada:**\n    *   Se a pergunta do usuário for muito vaga e você não conseguir determinar com segurança qual ferramenta usar, ou se a informação necessária para a consulta estiver faltando (ex: \"qual o status do pedido?\" sem o ID do pedido), peça educadamente por esclarecimentos. Ex: \"Para buscar essa informação, poderia me informar o [dado faltante, ex: número do pedido]?\" ou \"Poderia especificar se sua dúvida sobre 'metas' se refere a metas gerais da empresa (`Supabase_metas`) ou metas de vendas (`Supabase_metasvenda`)?\"\n    *   Se a pergunta do usuário claramente não se encaixa na especialidade de NENHUMA das ferramentas disponíveis, informe que você não pode ajudar com esse tipo de solicitação. Ex: \"Desculpe, só posso fornecer informações contidas em nossas bases de dados sobre [liste alguns exemplos de tópicos cobertos]. Não tenho acesso a [tópico fora do escopo].\"\n\n### REGRAS PARA FORMATAÇÃO DA RESPOSTA E RESTRIÇÕES ###\n1.  **Fidelidade Absoluta aos Dados da Ferramenta:** Sua resposta deve ser ESTRITAMENTE e UNICAMENTE baseada nos dados retornados pela ferramenta Supabase que você selecionou e utilizou.\n2.  **PROIBIDO INVENTAR:** É CRUCIAL que você NUNCA crie, invente, infira, deduza ou suponha informações que não estejam explicitamente presentes nos resultados da busca da ferramenta.\n3.  **Dados Não Encontrados:** Se a ferramenta selecionada não retornar dados para a consulta, ou se a informação solicitada não existir naquela base de dados específica, informe clara e diretamente ao usuário que a informação não foi encontrada. Exemplo: \"Consultei a tabela/view `[Nome da Ferramenta usada]` e não localizei informações sobre [tópico da pergunta do usuário].\"\n4.  **Concisão e Objetividade:** Responda de forma direta e objetiva. Se a ferramenta retornar muitos dados, tente resumir ou apresentar os mais relevantes, a menos que o usuário peça todos os detalhes.\n5.  **Linguagem Natural:** Apresente a resposta em linguagem natural e clara para o usuário.", "options": {"systemMessage": "voce e um assistente de vendas"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.9, "position": [-40, 220], "id": "f5adcb1b-b859-4357-9f8c-b56737f6a65d", "name": "AI Agent"}, {"parameters": {"sessionIdType": "customKey", "sessionKey": "={{ $json.idCliente }}", "tableName": "=n8n_chat_histories", "contextWindowLength": 15}, "type": "@n8n/n8n-nodes-langchain.memoryPostgresChat", "typeVersion": 1.3, "position": [420, 80], "id": "4513ed64-f8de-451f-8d60-e7f19ec1d2cc", "name": "Postgres Chat Memory", "credentials": {"postgres": {"id": "mBQ6JPcY1J0agKpf", "name": "Postgres account"}}}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatDeepSeek", "typeVersion": 1, "position": [-280, 100], "id": "a63d9dd2-32e6-4dba-a310-4bc92a407307", "name": "DeepSeek Chat Model", "credentials": {"deepSeekApi": {"id": "wWGUoy88OIB4nIqs", "name": "DeepSeek account"}}}, {"parameters": {"descriptionType": "manual", "toolDescription": "Acessa a tabela `vendas` para fornecer informações sobre registros de vendas concluídas. Contém dados como ID da venda, data da venda, valor total da venda, ID do cliente associado, ID do vendedor responsável e status geral do pedido.\n\nUse esta ferramenta para perguntas como:\n- \"Qual foi o total de vendas em [data/período específico]?\"\n- \"Liste as vendas feitas para o cliente [nome/ID do cliente].\"\n- \"Quais foram as vendas realizadas pelo vendedor [nome/ID do vendedor] no último mês?\"\n- \"Qual o status do pedido [número do pedido]?\"\n\nIMPORTANTE: Para obter os PRODUTOS/ITENS ESPECÍFICOS de uma venda, utilize a ferramenta `Supabase_vendas_items` (geralmente fornecendo o ID da venda obtido aqui).", "operation": "getAll", "tableId": "vendas"}, "type": "n8n-nodes-base.supabaseTool", "typeVersion": 1, "position": [-800, 440], "id": "e2c63116-3116-412c-a11e-f0a1ae6ee7aa", "name": "Supabase_vendas", "credentials": {"supabaseApi": {"id": "9tsTrTPf38yra4Sm", "name": "Supabase account"}}}, {"parameters": {"respondWith": "json", "responseBody": "={\n  \"resposta\": {{ JSON.stringify($json.output) }}\n}", "options": {}}, "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1.2, "position": [720, 220], "id": "408db1cd-924c-4fd2-886b-16e86f28a4fa", "name": "Respond to Webhook"}, {"parameters": {"descriptionType": "manual", "toolDescription": "Acessa a tabela `clientes` para fornecer informações cadastrais sobre os clientes. Contém dados como ID do cliente, nome, e-mail, telefone, endereço e data de cadastro.\n\nUse esta ferramenta para perguntas como:\n- \"Qual o telefone do cliente [nome do cliente]?\"\n- \"Quantos clientes foram cadastrados no último trimestre?\"\n- \"O cliente com e-mail [<EMAIL>] está ativo?\"\n\nNÃO USE para histórico de compras detalhado (use `Supabase_vendas`).", "operation": "getAll", "tableId": "clientes"}, "type": "n8n-nodes-base.supabaseTool", "typeVersion": 1, "position": [-680, 500], "id": "7bcadd8d-6bee-4ea2-956b-c4b667346aa7", "name": "Supabase_clientes", "credentials": {"supabaseApi": {"id": "9tsTrTPf38yra4Sm", "name": "Supabase account"}}}, {"parameters": {"descriptionType": "manual", "toolDescription": "Acessa a tabela `produtos` para fornecer informações sobre o catálogo de produtos. Contém dados como ID do produto, nome, descrição, categoria, preço de venda e possivelmente quantidade em estoque.\n\nUse esta ferramenta para perguntas como:\n- \"Qual o preço do produto [nome/ID do produto]?\"\n- \"Liste todos os produtos da categoria [nome da categoria].\"\n- \"O produto [nome do produto] está disponível em estoque?\"", "operation": "getAll", "tableId": "produtos"}, "type": "n8n-nodes-base.supabaseTool", "typeVersion": 1, "position": [-560, 440], "id": "5cc0ff4c-b7e7-4757-a606-56886d8cea6b", "name": "Supabase_produtos", "credentials": {"supabaseApi": {"id": "9tsTrTPf38yra4Sm", "name": "Supabase account"}}}, {"parameters": {"descriptionType": "manual", "toolDescription": "Acessa a tabela ou view `fluxo_caixa` para fornecer uma visão consolidada das entradas e saídas financeiras da empresa. Ideal para totais e balanços em períodos específicos.\n\nUse esta ferramenta para perguntas como:\n- \"Qual foi o saldo do fluxo de caixa no mês passado?\"\n- \"Qual o total de receitas e despesas em [período]?\"\n\nPara detalhes de transações INDIVIDUAIS, use `Supabase_movimentos_caixa` ou `Supabase_ultimas_transacoes_views`.", "operation": "getAll", "tableId": "fluxo_caixa"}, "type": "n8n-nodes-base.supabaseTool", "typeVersion": 1, "position": [-420, 500], "id": "9ee7b597-28bc-4d21-a1aa-b4bacecbfa8d", "name": "Supabase_fluxo_caixa", "credentials": {"supabaseApi": {"id": "9tsTrTPf38yra4Sm", "name": "Supabase account"}}}, {"parameters": {"descriptionType": "manual", "toolDescription": "Acessa a tabela `despesas` para registrar e consultar todas as despesas da empresa. Contém dados como ID da despesa, data, descrição, categoria da despesa, valor e fornecedor associado (se aplicável).\n\nUse esta ferramenta para perguntas como:\n- \"Qual foi o total de despesas com [categoria] no último mês?\"\n- \"Liste as despesas pagas ao fornecedor [ID/nome do fornecedor].\"\n- \"Quais foram as despesas do dia [data]?\"", "operation": "getAll", "tableId": "despesas"}, "type": "n8n-nodes-base.supabaseTool", "typeVersion": 1, "position": [-280, 440], "id": "814a4bc4-a57c-471f-bcba-82cb20370119", "name": "Supabase_despesas", "credentials": {"supabaseApi": {"id": "9tsTrTPf38yra4Sm", "name": "Supabase account"}}}, {"parameters": {"descriptionType": "manual", "toolDescription": "Acessa a tabela `metas` para informações sobre metas gerais da empresa ou de departamentos (ex: metas financeiras globais, metas operacionais, metas de satisfação do cliente). Contém dados como ID da meta, descrição, período, valor alvo e progresso atual.\n\nUse esta ferramenta para perguntas como:\n- \"Qual a meta de faturamento global para este trimestre?\"\n- \"Como está o progresso da meta [nome/ID da meta]?\"\n\nNÃO USE para metas específicas de vendas (para isso, utilize `Supabase_metasvenda`).", "operation": "getAll", "tableId": "metas"}, "type": "n8n-nodes-base.supabaseTool", "typeVersion": 1, "position": [-140, 500], "id": "ad1b7b2c-509e-4588-b7d0-226d0249f7ee", "name": "Supabase_metas", "credentials": {"supabaseApi": {"id": "9tsTrTPf38yra4Sm", "name": "Supabase account"}}}, {"parameters": {"descriptionType": "manual", "toolDescription": "Acessa a tabela `receitas` para registrar e consultar todas as receitas da empresa. Contém dados como ID da receita, data, descrição, origem da receita (ex: venda, serviço), valor e cliente associado (se aplicável).\n\nUse esta ferramenta para perguntas como:\n- \"Qual foi o total de receitas provenientes de [origem] no último mês?\"\n- \"Liste as receitas do dia [data].\"", "operation": "getAll", "tableId": "receitas"}, "type": "n8n-nodes-base.supabaseTool", "typeVersion": 1, "position": [-20, 440], "id": "3d1de5ed-cadd-45ee-8d5b-c0b671d4c190", "name": "Supabase_receitas", "credentials": {"supabaseApi": {"id": "9tsTrTPf38yra4Sm", "name": "Supabase account"}}}, {"parameters": {"descriptionType": "manual", "toolDescription": "Acessa a tabela `metasvenda` para informações sobre metas específicas de vendas para produtos, vendedores, equipes ou períodos. Contém dados como ID da meta de venda, descrição, produto/vendedor/equipe alvo, período, valor alvo e progresso atual.\n\nUse esta ferramenta para perguntas como:\n- \"Qual a meta de vendas para o vendedor [ID/nome do vendedor] este mês?\"\n- \"Como está o progresso da meta de vendas do produto [ID/nome do produto]?\"", "operation": "getAll", "tableId": "metas_venda"}, "type": "n8n-nodes-base.supabaseTool", "typeVersion": 1, "position": [120, 500], "id": "94860fcf-e9c6-4d5c-8f35-242be264cf1d", "name": "Supabase_metas_venda", "credentials": {"supabaseApi": {"id": "9tsTrTPf38yra4Sm", "name": "Supabase account"}}}, {"parameters": {"descriptionType": "manual", "toolDescription": "Acessa a tabela `regras_comissao` para detalhar as regras, percentuais e condições para o cálculo de comissões de vendas. Contém dados como ID da regra, tipo de comissão, percentual, condições de aplicabilidade (ex: por produto, por valor de venda).\n\nUse esta ferramenta para perguntas como:\n- \"Qual o percentual de comissão para vendas do produto [ID/nome do produto]?\"\n- \"Quais são as regras de comissão para vendedores iniciantes?\"", "operation": "getAll", "tableId": "regras_comissao"}, "type": "n8n-nodes-base.supabaseTool", "typeVersion": 1, "position": [260, 440], "id": "31ce990a-4688-46bd-a2c6-69fcc7ffd873", "name": "Supabase_regras_comissao", "credentials": {"supabaseApi": {"id": "9tsTrTPf38yra4Sm", "name": "Supabase account"}}}, {"parameters": {"descriptionType": "manual", "toolDescription": "Acessa a tabela `profiles` para informações sobre perfis de usuários internos do sistema (funcionários). Contém dados como ID do usuário, nome, e-mail, cargo, departamento e permissões de acesso.\n\nUse esta ferramenta para perguntas como:\n- \"Qual o e-mail do funcionário [nome do funcionário]?\"\n- \"Quem são os usuários do departamento de [nome do departamento]?\"\n\nNÃO USAR para informações de clientes finais (para isso, use `Supabase_clientes`).", "operation": "getAll", "tableId": "profiles"}, "type": "n8n-nodes-base.supabaseTool", "typeVersion": 1, "position": [400, 500], "id": "13ebc3aa-de69-43b6-b55d-9c1973b1cfe0", "name": "Supabase_profile", "credentials": {"supabaseApi": {"id": "9tsTrTPf38yra4Sm", "name": "Supabase account"}}}, {"parameters": {"descriptionType": "manual", "toolDescription": "Acessa a tabela `comissoes` para consultar valores de comissões de vendas já calculadas, pagas ou pendentes para cada vendedor. Contém dados como ID da comissão, ID do vendedor, ID da venda associada, valor da comissão, status (pendente/paga) e data de pagamento.\n\nUse esta ferramenta para perguntas como:\n- \"Qual o valor total de comissões pendentes para o vendedor [ID/nome do vendedor]?\"\n- \"A comissão da venda [ID da venda] já foi paga?\"", "operation": "getAll", "tableId": "comissoes"}, "type": "n8n-nodes-base.supabaseTool", "typeVersion": 1, "position": [540, 440], "id": "011da002-62eb-4709-8e7d-f11d604520e7", "name": "Supabase_comissoes", "credentials": {"supabaseApi": {"id": "9tsTrTPf38yra4Sm", "name": "Supabase account"}}}, {"parameters": {"descriptionType": "manual", "toolDescription": "Acessa uma visualização (view) chamada `ultimas_transacoes_views` que mostra as transações financeiras mais recentes, tanto entradas quanto saídas. Contém dados como ID da transação, data, tipo (entrada/saída), valor e breve descrição.\n\nUse esta ferramenta para perguntas como:\n- \"Quais foram as últimas 5 transações financeiras?\"\n- \"Houve alguma entrada de valor superior a [valor] hoje?\"", "operation": "getAll", "tableId": "ultimas_transacoes_view"}, "type": "n8n-nodes-base.supabaseTool", "typeVersion": 1, "position": [680, 500], "id": "932c9b3b-1605-47ce-aa26-e09d3bea1ba6", "name": "Supabase_ultimas_transacoes_views", "credentials": {"supabaseApi": {"id": "9tsTrTPf38yra4Sm", "name": "Supabase account"}}}, {"parameters": {"descriptionType": "manual", "toolDescription": "Acessa a tabela `fornecedores` para informações cadastrais e de contato de fornecedores de produtos ou serviços para a empresa. Contém dados como ID do fornecedor, nome, CNPJ/CPF, contato, endereço e tipo de produto/serviço fornecido.\n\nUse esta ferramenta para perguntas como:\n- \"Qual o telefone do fornecedor [nome do fornecedor]?\"\n- \"Liste nossos fornecedores de [tipo de produto/serviço].\"", "operation": "getAll", "tableId": "fornecedores"}, "type": "n8n-nodes-base.supabaseTool", "typeVersion": 1, "position": [820, 440], "id": "22ad95eb-a903-4b20-a4ee-f7fdf2fd7ef3", "name": "Supabase_fornecedores", "credentials": {"supabaseApi": {"id": "9tsTrTPf38yra4Sm", "name": "Supabase account"}}}, {"parameters": {"descriptionType": "manual", "toolDescription": "Acessa a tabela `movimentos_caixa` para registrar detalhadamente cada movimentação individual no caixa da empresa. Contém dados como ID do movimento, data/hora, tipo (entrada/saída), valor, descrição/referência e saldo após o movimento.\n\nUse esta ferramenta para perguntas como:\n- \"Liste todos os movimentos de caixa do dia [data].\"\n- \"Qual foi a descrição do movimento de saída de [valor] no dia [data]?\"", "operation": "getAll", "tableId": "movimentos_caixa"}, "type": "n8n-nodes-base.supabaseTool", "typeVersion": 1, "position": [960, 500], "id": "0c9cf6c7-7f05-4485-8b42-46612548bdca", "name": "Supabase_movimentos_caixa", "credentials": {"supabaseApi": {"id": "9tsTrTPf38yra4Sm", "name": "Supabase account"}}}, {"parameters": {"descriptionType": "manual", "toolDescription": "Acessa a tabela `vendas_items` para detalhar os itens específicos que compõem cada venda. Contém dados como ID do item da venda, ID da venda (referência à tabela `vendas`), ID do produto, quantidade, preço unitário e valor total do item.\n\nUse esta ferramenta para perguntas como:\n- \"Quais produtos foram vendidos no pedido [ID da venda]?\"\n- \"Quantas unidades do produto [ID/nome do produto] foram vendidas na venda [ID da venda]?\"\n\nGeralmente consultada após obter um ID de venda da ferramenta `Supabase_vendas`.", "operation": "getAll", "tableId": "vendas_items"}, "type": "n8n-nodes-base.supabaseTool", "typeVersion": 1, "position": [1080, 440], "id": "ce216684-68f5-4890-996d-ac740855caf7", "name": "Supabase_vendas_items", "credentials": {"supabaseApi": {"id": "9tsTrTPf38yra4Sm", "name": "Supabase account"}}}, {"parameters": {"descriptionType": "manual", "toolDescription": "Acessa a tabela `categorias` para listar e gerenciar categorias usadas para classificar diversos itens no sistema (ex: produtos, despesas, receitas). Contém dados como ID da categoria, nome da categoria e descrição.\n\nUse esta ferramenta para perguntas como:\n- \"Quais são as categorias de produtos disponíveis?\"\n- \"A categoria '[nome da categoria]' existe para despesas?\"", "operation": "getAll", "tableId": "categorias"}, "type": "n8n-nodes-base.supabaseTool", "typeVersion": 1, "position": [-260, 680], "id": "bdb78764-2a9c-4213-87d1-51785f71f318", "name": "Supabase_categorias", "credentials": {"supabaseApi": {"id": "9tsTrTPf38yra4Sm", "name": "Supabase account"}}}, {"parameters": {"descriptionType": "manual", "toolDescription": "Acessa a tabela `reservas` para informações sobre reservas feitas por clientes (ex: reserva de produtos, agendamento de serviços). Contém dados como ID da reserva, ID do cliente, ID do produto/serviço, data da reserva, data do agendamento e status da reserva (confirmada, pendente, cancelada).\n\nUse esta ferramenta para perguntas como:\n- \"Quais reservas o cliente [ID/nome do cliente] possui?\"\n- \"Qual o status da reserva [ID da reserva]?\"", "operation": "getAll", "tableId": "reservas"}, "type": "n8n-nodes-base.supabaseTool", "typeVersion": 1, "position": [-80, 680], "id": "51deb446-10d2-47dc-a821-3db04c943c53", "name": "Supabase_reservas", "credentials": {"supabaseApi": {"id": "9tsTrTPf38yra4Sm", "name": "Supabase account"}}}, {"parameters": {"descriptionType": "manual", "toolDescription": "Acessa a tabela `vendedores` para informações cadastrais e de desempenho dos vendedores da empresa. Contém dados como ID do vendedor, nome, e-mail, equipe e metas individuais (se não estiverem em `Supabase_metasvenda`).\n\nUse esta ferramenta para perguntas como:\n- \"Qual o e-mail do vendedor [nome do vendedor]?\"\n- \"Liste todos os vendedores da equipe [nome da equipe].\"", "operation": "getAll", "tableId": "vendedores"}, "type": "n8n-nodes-base.supabaseTool", "typeVersion": 1, "position": [80, 680], "id": "2795c39a-63ba-463b-a430-5a2fafd350ab", "name": "Supabase_vendedores", "credentials": {"supabaseApi": {"id": "9tsTrTPf38yra4Sm", "name": "Supabase account"}}}, {"parameters": {"descriptionType": "manual", "toolDescription": "Acessa a tabela `resgates_pontos` para informações sobre resgates de pontos em programas de fidelidade ou recompensas. Contém dados como ID do resgate, ID do cliente, data do resgate, quantidade de pontos resgatados e prêmio/benefício obtido.\n\nUse esta ferramenta para perguntas como:\n- \"Quais resgates de pontos o cliente [ID/nome do cliente] realizou?\"\n- \"Quantos pontos foram necessários para o resgate [ID do resgate/prêmio]?\"", "operation": "getAll", "tableId": "resgates_pontos"}, "type": "n8n-nodes-base.supabaseTool", "typeVersion": 1, "position": [240, 680], "id": "756bf26a-81a2-4654-90d9-a9a6e3486cd0", "name": "Supabase_resgates_pontos", "credentials": {"supabaseApi": {"id": "9tsTrTPf38yra4Sm", "name": "Supabase account"}}}, {"parameters": {"httpMethod": "POST", "path": "brecho", "responseMode": "responseNode", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-740, 100], "id": "e056428f-7d9e-4306-b935-afe46864cbf8", "name": "Webhook", "webhookId": "9bb927b0-04ce-49d3-a488-f9d029031044"}], "pinData": {}, "connections": {"Edit Fields": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "Postgres Chat Memory": {"ai_memory": [[{"node": "AI Agent", "type": "ai_memory", "index": 0}]]}, "AI Agent": {"main": [[{"node": "Respond to Webhook", "type": "main", "index": 0}]]}, "DeepSeek Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Supabase_vendas": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Supabase_clientes": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Supabase_produtos": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Supabase_fluxo_caixa": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Supabase_despesas": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Supabase_metas": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Supabase_receitas": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Supabase_metas_venda": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Supabase_regras_comissao": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Supabase_profile": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Supabase_comissoes": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Supabase_ultimas_transacoes_views": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Supabase_fornecedores": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Supabase_movimentos_caixa": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Supabase_vendas_items": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Supabase_categorias": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Supabase_reservas": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Supabase_vendedores": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Supabase_resgates_pontos": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Webhook": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}]]}}, "active": true, "settings": {"executionOrder": "v1"}, "versionId": "e82753d7-e915-45a3-b1ba-4e5d8e521d1e", "meta": {"templateCredsSetupCompleted": true, "instanceId": "e898b45972b29bf21dbc778c4010801571e7e95c50ad724996f0c79d6cd7a946"}, "id": "jDCLcolqT1eTe1TF", "tags": []}