// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://nfomheksnqsuxtwnijgg.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5mb21oZWtzbnFzdXh0d25pamdnIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDU3NzA4NTYsImV4cCI6MjA2MTM0Njg1Nn0.iW2_VeIngchX03JQ08dzKTG8-HdqIfKSARBXwHCPgYE";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);