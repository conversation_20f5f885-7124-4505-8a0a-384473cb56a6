# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Visão Geral do Projeto

GESTOR BRECHÓ é um sistema completo de gestão para brechós construído com React, TypeScript e Supabase. Ele automatiza processos operacionais, financeiros e administrativos para empresas de moda sustentável.

## Regras Importantes

1. **Sempre comunicar em PT-BR** - Toda comunicação deve ser em português brasileiro
2. **Nunca usar dados mockados ou hardcoded** - Sempre usar dados reais do banco de dados
3. **Nunca usar emojis** - Não incluir emojis em outputs, markdown ou código

## Comandos de Desenvolvimento

```bash
# Instalar dependências
npm install

# Iniciar servidor de desenvolvimento (porta 8080)
npm run dev

# Build para produção
npm run build

# Build para ambiente de desenvolvimento
npm run build:dev

# Executar ESLint
npm run lint

# Visualizar build de produção
npm run preview
```

## Arquitetura de Alto Nível

### Stack Tecnológico
- **Frontend**: React 18 com TypeScript, Vite, React Router v6
- **Componentes UI**: Shadcn/ui, Tailwind CSS, Radix UI primitives
- **Gerenciamento de Estado**: React Query (TanStack Query), React Context API
- **Backend**: Supabase (PostgreSQL, Auth, REST APIs, Funções Serverless)
- **Formulários**: React Hook Form com validação Zod
- **Gráficos**: Recharts

### Estrutura do Projeto
```
src/
├── components/      # Componentes UI reutilizáveis organizados por funcionalidade
├── pages/          # Componentes de rota e layouts de página
├── services/       # Lógica de negócio e integrações Supabase
├── hooks/          # Hooks React customizados
├── lib/            # Utilitários, validações e tipos
├── contexts/       # Provedores de contexto React
└── types/          # Definições de tipos TypeScript
```

### Padrões Arquiteturais Principais
1. **Padrão Service Layer**: Todas as interações com Supabase passam por arquivos em `src/services/`
2. **Validação de Formulários**: Schemas Zod em `src/lib/validations/` com React Hook Form
3. **Organização de Componentes**: Agrupamento por funcionalidade (ex: `components/financeiro/`, `components/vendas/`)
4. **Estrutura de Rotas**: Rotas aninhadas em `pages/app/` para seções autenticadas

## Módulos de Negócio Principais

### 1. Gestão de Produtos (`produtos`)
- Cadastro de produtos com múltiplos atributos e upload de imagens
- Sistema de categorização flexível
- Controle de estoque com lógica de reserva

### 2. Vendas (`vendas`)
- Vendas com múltiplos itens e formas de pagamento
- Geração automática de parcelas
- Cálculo de comissões para vendedores
- Integração com estoque

### 3. Gestão Financeira (`financeiro`)
- Controle de receitas e despesas categorizadas
- Monitoramento de fluxo de caixa
- Transações recorrentes
- Metas financeiras e relatórios

### 4. Gestão de Clientes (`clientes`)
- Perfis completos de clientes
- Histórico de transações
- Sistema de pontos e resgates

### 5. Fornecedores (`fornecedores`)
- Cadastro com validação fiscal
- Mapeamento geográfico por estado
- Acompanhamento de desempenho

### 6. Vendedores (`vendedores`)
- Regras e cálculo de comissões
- Metas e métricas de desempenho
- Gerenciamento de níveis de acesso

### 7. Assistente IA (`ia`)
- Integração com webhook n8n para respostas generativas
- Interface de chat contextual
- Capacidade de consultas ao banco de dados

## Serviços Críticos e Pontos de Integração

### Configuração do Cliente Supabase
- Inicialização do cliente: `src/integrations/supabase/client.ts`
- Variáveis de ambiente: `VITE_SUPABASE_URL`, `VITE_SUPABASE_KEY`

### Padrões de Serviço
```typescript
// Exemplo de padrão de serviço padrão
export async function getItems() {
  const { data, error } = await supabase
    .from('table_name')
    .select('*')
    .order('created_at', { ascending: false });
    
  if (error) throw error;
  return data;
}
```

### Configuração do TypeScript
- `noImplicitAny: false` - Any implícito permitido
- `noUnusedParameters: false` - Parâmetros não usados permitidos
- `strictNullChecks: false` - Verificações null relaxadas
- Path alias: `@/*` mapeado para `./src/*`

### Tratamento de Data/Hora
- Todos os timestamps usam formato com timezone Brasil (America/Sao_Paulo)
- Hook customizado `useDateTimeBR` para formatação consistente
- Banco armazena UTC, frontend exibe horário local

## Diretrizes de Desenvolvimento

### Estilo de Código
- TypeScript com regras relaxadas (implicit any permitido, parâmetros não usados permitidos)
- Preferir componentes UI existentes do Shadcn/ui
- Seguir padrões existentes no código
- Usar imports absolutos com prefixo `@/`

### Padrões Comuns
1. **Busca de Dados**: Usar React Query com funções de serviço
2. **Formulários**: React Hook Form + schema Zod + componentes Shadcn
3. **Tratamento de Erros**: Notificações toast para feedback ao usuário
4. **Estados de Carregamento**: Spinners e skeletons consistentes

### Configuração de Ambiente
```env
VITE_SUPABASE_URL=sua_url_supabase
VITE_SUPABASE_KEY=sua_chave_anon
```

### Porta do Servidor de Desenvolvimento
- O servidor Vite roda na porta **8080** (configurado em vite.config.ts)
- Host configurado como `::` para aceitar conexões IPv4 e IPv6

## Considerações Importantes

1. **Sem Framework de Testes**: Atualmente sem testes automatizados implementados
2. **Deploy**: Configurado para plataforma Lovable com auto-scaling (Projeto ID: 6b25c16a-6ade-4958-b76e-2b90ccdf57a0)
3. **Autenticação**: Supabase Auth com rotas protegidas
4. **Upload de Arquivos**: Imagens armazenadas no Supabase Storage
5. **Funcionalidades Real-time**: Não implementadas mas prontas para Supabase
6. **Componente Tagger**: Lovable tagger ativo apenas em modo desenvolvimento

## Débitos Técnicos Conhecidos
- Comentários TODO espalhados pelo código (7 identificados)
- Logs de debug precisam ser removidos antes da produção
- Implementação de testes pendente
- Alguns componentes precisam de otimização de performance

## Regras de Negócio Principais
1. Vendas atualizam automaticamente níveis de estoque
2. Comissões calculadas baseadas em regras configuráveis
3. Transações recorrentes processadas automaticamente
4. Categorias financeiras separadas de categorias de produtos
5. Controle de acesso multinível para diferentes tipos de usuário