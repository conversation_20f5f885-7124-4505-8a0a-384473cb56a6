-- Criar tabela para histórico de chat se não existir
CREATE TABLE IF NOT EXISTS public.ia_chat_historico (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    user_message TEXT NOT NULL,
    assistant_message TEXT NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    metadata JSONB DEFAULT '{}'::jsonb
);

-- Criar índices para melhor performance
CREATE INDEX IF NOT EXISTS idx_ia_chat_historico_user_id ON public.ia_chat_historico(user_id);
CREATE INDEX IF NOT EXISTS idx_ia_chat_historico_created_at ON public.ia_chat_historico(created_at DESC);

-- Habilitar RLS (Row Level Security)
ALTER TABLE public.ia_chat_historico ENABLE ROW LEVEL SECURITY;

-- Política para usuários verem apenas suas próprias conversas
CREATE POLICY "Usuários podem ver apenas suas próprias conversas" 
    ON public.ia_chat_historico
    FOR ALL 
    USING (auth.uid() = user_id);

-- Conceder permissões necessárias
GRANT ALL ON public.ia_chat_historico TO authenticated;
GRANT USAGE ON SEQUENCE public.ia_chat_historico_id_seq TO authenticated;